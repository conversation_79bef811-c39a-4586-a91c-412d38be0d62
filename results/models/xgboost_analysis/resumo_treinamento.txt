RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

MODELO BINÁRIO:
  • Tipo: XGBoost Binário
  • Classes: 0=Venda, 1=Compra
  • Função de perda: logloss
  • Threshold de probabilidade: 0.5 (sinais só gerados se prob > 0.5)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Am<PERSON>ud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator, High Max 50d, Low Min 50d (13 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 113
  • Acurácia geral: 0.756

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (113):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Segunda
  12. Terca
  13. Quarta
  14. Quinta
  15. Sexta
  16. Mes_1
  17. Mes_2
  18. Mes_3
  19. Mes_4
  20. Mes_5
  21. Mes_6
  22. Mes_7
  23. Mes_8
  24. Mes_9
  25. Mes_10
  26. Mes_11
  27. Mes_12
  28. Quarter_1
  29. Quarter_2
  30. Quarter_3
  31. Quarter_4
  32. Last_Day_Quarter
  33. Pre_Feriado_Brasil
  34. Volume_Lag_1
  35. Volume_Lag_2
  36. Volume_Lag_3
  37. Volume_Lag_4
  38. Volume_Lag_5
  39. Spread_Lag_1
  40. Spread_Lag_2
  41. Spread_Lag_3
  42. Spread_Lag_4
  43. Spread_Lag_5
  44. Volatilidade_Lag_1
  45. Volatilidade_Lag_2
  46. Volatilidade_Lag_3
  47. Volatilidade_Lag_4
  48. Volatilidade_Lag_5
  49. Parkinson_Volatility_Lag_1
  50. Parkinson_Volatility_Lag_2
  51. Parkinson_Volatility_Lag_3
  52. Parkinson_Volatility_Lag_4
  53. Parkinson_Volatility_Lag_5
  54. MFI_Lag_1
  55. MFI_Lag_2
  56. MFI_Lag_3
  57. MFI_Lag_4
  58. MFI_Lag_5
  59. EMV_Lag_1
  60. EMV_Lag_2
  61. EMV_Lag_3
  62. EMV_Lag_4
  63. EMV_Lag_5
  64. EMV_MA_Lag_1
  65. EMV_MA_Lag_2
  66. EMV_MA_Lag_3
  67. EMV_MA_Lag_4
  68. EMV_MA_Lag_5
  69. Amihud_Lag_1
  70. Amihud_Lag_2
  71. Amihud_Lag_3
  72. Amihud_Lag_4
  73. Amihud_Lag_5
  74. Roll_Spread_Lag_1
  75. Roll_Spread_Lag_2
  76. Roll_Spread_Lag_3
  77. Roll_Spread_Lag_4
  78. Roll_Spread_Lag_5
  79. Hurst_Lag_1
  80. Hurst_Lag_2
  81. Hurst_Lag_3
  82. Hurst_Lag_4
  83. Hurst_Lag_5
  84. Vol_per_Volume_Lag_1
  85. Vol_per_Volume_Lag_2
  86. Vol_per_Volume_Lag_3
  87. Vol_per_Volume_Lag_4
  88. Vol_per_Volume_Lag_5
  89. CMF_Lag_1
  90. CMF_Lag_2
  91. CMF_Lag_3
  92. CMF_Lag_4
  93. CMF_Lag_5
  94. AD_Line_Lag_1
  95. AD_Line_Lag_2
  96. AD_Line_Lag_3
  97. AD_Line_Lag_4
  98. AD_Line_Lag_5
  99. VO_Lag_1
  100. VO_Lag_2
  101. VO_Lag_3
  102. VO_Lag_4
  103. VO_Lag_5
  104. High_Max_50_Lag_1
  105. High_Max_50_Lag_2
  106. High_Max_50_Lag_3
  107. High_Max_50_Lag_4
  108. High_Max_50_Lag_5
  109. Low_Min_50_Lag_1
  110. Low_Min_50_Lag_2
  111. Low_Min_50_Lag_3
  112. Low_Min_50_Lag_4
  113. Low_Min_50_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.756
  • Distribuição das Predições:
    - Venda: 8527 (50.1%)
    - Compra: 8491 (49.9%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
